# 肺功能数据管理平台 - 部署与运维手册

本文档是项目的“操作手册”，涵盖了从环境准备、自动化部署到日常维护和故障排查的所有内容。

## 目录

1.  [**环境要求**](#1-环境要求)
2.  [**部署流程**](#2-部署流程)
    *   [2.1 一键部署 (推荐)](#21-一键部署-推荐)
    *   [2.2 手动部署步骤](#22-手动部署步骤)
    *   [2.3 环境变量配置](#23-环境变量配置)
3.  [**进程管理 (PM2)**](#3-进程管理-pm2)
    *   [3.1 快速入门](#31-快速入门)
    *   [3.2 常用命令](#32-常用命令)
    *   [3.3 集群模式与内存管理](#33-集群模式与内存管理)
4.  [**日常维护**](#4-日常维护)
    *   [4.1 应用更新](#41-应用更新)
    *   [4.2 监控与日志](#42-监控与日志)
    *   [4.3 备份策略](#43-备份策略)
5.  [**故障排查 (Troubleshooting)**](#5-故障排查-troubleshooting)
    *   [5.1 应用启动问题](#51-应用启动问题)
    *   [5.2 数据库连接问题](#52-数据库连接问题)
    *   [5.3 Webhook接收问题](#53-webhook接收问题)
    *   [5.4 Nginx与网络问题](#54-nginx与网络问题)

---

## 1. 环境要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **磁盘**: 20GB 可用空间
- **操作系统**: Linux (推荐 Ubuntu 20.04+)

### 软件依赖
- **Node.js**: 18.x 或更高版本
- **PM2**: 最新版本
- **数据库**: MySQL 8.0+
- **反向代理**: Nginx (推荐)

---

## 2. 部署流程

### 2.1 一键部署 (推荐)

对于新服务器，推荐使用项目提供的一键部署脚本，它会自动完成环境准备、项目部署和服务配置。

```bash
# 上传脚本：
scp /Users/<USER>/PythonProjects/free_lung_function_project_admin/scripts/deploy-traditional.sh tencent:/www/wwwroot/script
# 进入文档
cd /www/wwwroot/script
# 赋予执行权限
chmod +x deploy-traditional.sh
# 运行脚本
./deploy-traditional.sh
```



### 2.2 手动部署步骤

1.  **环境准备**
    ```bash
    # 更新系统
    sudo apt update && sudo apt upgrade -y

    # 安装Node.js 18
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt-get install -y nodejs

    # 安装PM2和Nginx
    npm install -g pm2
    sudo apt install -y nginx
    ```

2.  **项目部署**
    ```bash
    # 克隆项目
    git clone https://github.com/peckbyte/free_lung_function_project_admin.git
    cd free_lung_function_project_admin

    # 安装依赖
    npm install

    # 构建项目
    npm run build
    ```

3.  **配置Nginx**
    创建一个Nginx配置文件 `sudo nano /etc/nginx/sites-available/your-domain.com` 并启用它。
    ```nginx
    server {
        listen 80;
        server_name your-domain.com;

        location / {
            proxy_pass http://localhost:3011; # 确保端口与PM2配置一致
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;
        }
    }
    ```
    **SSL配置**: 推荐使用 `certbot` 自动配置HTTPS。
    ```bash
    sudo apt install certbot python3-certbot-nginx
    sudo certbot --nginx -d your-domain.com
    ```

### 2.3 环境变量配置

- 在项目根目录创建 `.env.production` 文件。
- 这是应用运行的**关键**，必须正确配置。

```env
# 应用配置
NODE_ENV=production
PORT=3011

# 数据库连接 (生产环境推荐使用内网地址)
DATABASE_URL="mysql://user:password@host:port/database"

# 认证密钥 (必须修改为随机字符串)
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-super-secret-key-for-nextauth"
JWT_SECRET="your-super-secret-key-for-jwt"

# 日志配置
LOG_LEVEL=info
LOG_FILE="/var/log/lung-function-admin.log"
```

---

## 3. 进程管理 (PM2)

PM2是Node.js应用的守护进程管理器，能确保应用在后台持续运行，并提供日志管理、性能监控等功能。

### 3.1 快速入门

项目已通过 `ecosystem.config.js` 进行了预配置。

```bash
# 启动生产环境
pm2 start ecosystem.config.js --env production

# 保存配置，以便服务器重启后自动运行
pm2 save

# 设置开机自启
pm2 startup
```

### 3.2 常用命令

项目提供了简化的管理脚本 `scripts/pm2-manager.sh`。

```bash
# 查看状态
./scripts/pm2-manager.sh status

# 查看实时日志
./scripts/pm2-manager.sh logs

# 重启应用
./scripts/pm2-manager.sh restart

# 零停机重载 (用于更新)
./scripts/pm2-manager.sh reload
```

### 3.3 集群模式与内存管理

- **集群模式**: 为了充分利用多核CPU，可以启动集群模式。
  ```bash
  pm2 start ecosystem.config.js --env production-cluster
  ```
- **内存限制**: `ecosystem.config.js` 中已配置 `max_memory_restart: '1G'`，当应用内存超过1GB时会自动重启，防止内存泄漏导致服务器崩溃。

---

## 4. 日常维护

### 4.1 应用更新

```bash
# 1. 进入项目目录
cd /path/to/your/project

# 2. 拉取最新代码
git pull origin main

# 3. 安装/更新依赖
npm install

# 4. 重新构建项目
npm run build

# 5. 运行数据库迁移 (如果有模型变更)
npx prisma db push

# 6. 零停机重载应用
pm2 reload all
```

### 4.2 监控与日志

- **健康检查**: 访问 `https://your-domain.com/api/health` 查看系统健康状态。
- **实时日志**: `pm2 logs` 或 `tail -f /var/log/lung-function-admin.log`。
- **性能监控**: `pm2 monit` 提供了一个实时的命令行监控界面。

### 4.3 备份策略

- **数据库备份**: 使用`mysqldump`定期备份数据库。
- **文件备份**: 定期备份用户上传的文件目录（如 `uploads/`）。

---

## 5. 故障排查 (Troubleshooting)

### 5.1 应用启动问题

- **症状**: `pm2 status` 显示 `errored`。
- **排查**: 
    1.  查看错误日志: `pm2 logs --err`。
    2.  检查 `.env.production` 环境变量是否正确。
    3.  检查端口是否被占用: `sudo lsof -i :3011`。

### 5.2 数据库连接问题

- **症状**: 应用日志出现 "Can't connect to MySQL server"。
- **排查**: 
    1.  检查`DATABASE_URL`是否正确。
    2.  在服务器上使用`mysql`客户端或`telnet`测试数据库连接。
    3.  检查云服务器的安全组/防火墙规则，确保数据库端口对应用服务器开放。

### 5.3 Webhook接收问题

- **症状**: 金数据后台提示推送失败，或数据未出现在平台中。
- **排查**: 
    1.  检查应用日志中是否有`webhook`相关的错误信息。
    2.  确认表单ID是否正确，以及该表单是否已在平台中配置并启用。
    3.  使用`curl`手动模拟金数据POST请求到您的Webhook URL，检查响应。

### 5.4 Nginx与网络问题

- **症状**: 访问域名出现 `502 Bad Gateway`。
- **排查**: 
    1.  `502`通常意味着Nginx无法连接到后端的Node.js应用。
    2.  检查PM2应用是否正在运行: `pm2 status`。
    3.  检查Nginx配置中的`proxy_pass`地址和端口是否正确。
    4.  查看Nginx错误日志: `sudo tail -f /var/log/nginx/error.log`。